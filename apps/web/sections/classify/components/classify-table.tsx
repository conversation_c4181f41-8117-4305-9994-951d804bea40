"use client";
import { AdvancedTable } from "@/components/advanced-table";
import { ErrorBoundary } from "@/components/error-boundary";
import { useFilteredFiles } from "@/sections/file/hooks/api";
import { useProjectData } from "@/sections/project/hooks/api";
import { extractGlossaryItemValue } from "@/providers/glossary-provider/extract-glossary-value";
import type { ProjectFileResponse, ProjectFileStatus } from "@/types";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { classifyTableConfig } from "../config/classify-table-config";
import NoClassifyData from "../no-data/no-classify-data";
import ClassifyTableSkeleton from "../skeleton/classify-table-skeleton";

interface ClassifyTableProps {
  projectId: string;
  selectedFiles: Set<string>;
  onFileSelect: (file: ProjectFileResponse, selected: boolean) => void;
  onRowClick: (file: ProjectFileResponse) => void;
  activeFileId?: string;
}

export const ClassifyTable = ({
  projectId,
  selectedFiles,
  onFileSelect,
  onRowClick,
  activeFileId,
}: ClassifyTableProps) => {
  // Use predefined filters for classification statuses
  const classifyFilters = {
    status: ["Classifying", "Classified"] as ProjectFileStatus[],
  };

  // Fetch project data for header info
  const { data: projectData, isLoading: isProjectLoading } = useProjectData(projectId);

  // Fetch files with classification filters
  const { files, isLoading, error, hasNextPage, isFetchingNextPage, refetch, handleScrollEnd } =
    useFilteredFiles({
      projectId,
      limit: 20,
      filters: classifyFilters,
      sort: undefined,
    });

  const columns = classifyTableConfig();

  const handleRowSelect = (file: ProjectFileResponse, selected: boolean) => {
    onFileSelect(file, selected);
  };

  if (isLoading || isProjectLoading) {
    return <ClassifyTableSkeleton />;
  }

  if (error) {
    <ErrorBoundary error={error} onRetry={() => refetch()} />;
  }

  return (
    <div className="h-full overflow-auto">
      {files.length === 0 ? (
        <NoClassifyData />
      ) : (
        <div className="space-y-4">
          {/* Project Info Header */}
          <div className="flex justify-between">
            <div>
              <h2 className="text-lg font-semibold capitalize text-gray-900">
                {projectData?.vessel || "Loading..."}
              </h2>
              <p className="text-sm text-gray-500">
                {extractGlossaryItemValue("Client", projectData?.categories || [])} |{" "}
                {projectData?.code} | {projectData?.assignee?.name || "Not assigned"} • Total Files:{" "}
                {(projectData?.fileCountMap?.Classifying || 0) +
                  (projectData?.fileCountMap?.Classified || 0) || files.length}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
                Export
                <Icons.download className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
                {" "}
                Filter
                <Icons.filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Table */}
          <AdvancedTable<ProjectFileResponse>
            data={files}
            columns={columns}
            onLoadMore={handleScrollEnd}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onRowSelect={handleRowSelect}
            selectedRows={selectedFiles}
            getRowId={(file) => file.id}
            isRowDisabled={(file) => file.status === "Classifying" || file.subStatus === "Failed"}
            onRowClick={onRowClick}
            activeRowId={activeFileId}
            className="mt-4"
          />
        </div>
      )}
    </div>
  );
};
