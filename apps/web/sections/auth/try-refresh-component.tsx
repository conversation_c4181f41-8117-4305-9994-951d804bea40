"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Session from "supertokens-auth-react/recipe/session";
import SuperTokens from "supertokens-auth-react";
import { AppLoading } from "@/components/loading";
import { Button } from "@repo/ui/components/button";

export const TryRefreshComponent = () => {
  const router = useRouter();
  const [didError, setDidError] = useState(false);

  useEffect(() => {
    void Session.attemptRefreshingSession()
      .then((hasSession) => {
        if (hasSession) {
          router.refresh();
        } else {
          SuperTokens.redirectToAuth();
        }
      })
      .catch(() => {
        setDidError(true);
      });
  }, [router]);

  /**
   * We add this check to make sure we handle the case where the refresh API fails with
   * an unexpected error
   */
  if (didError) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-lg font-semibold text-red-600">Session Refresh Failed</h2>
          <p className="mb-4 text-gray-600">
            We couldn't refresh your session. Please log in again.
          </p>
          <Button onClick={() => SuperTokens.redirectToAuth()} variant="default">
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  return <AppLoading message="Refreshing your session..." />;
};
