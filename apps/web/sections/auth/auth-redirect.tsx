"use client";
import { useEffect, useState } from "react";
import { redirect } from "next/navigation";
import Session from "supertokens-auth-react/recipe/session";
import { AppLoading } from "@/components/loading";

export function AuthRedirect({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isLoggedIn = await Session.doesSessionExist();
        if (isLoggedIn) {
          redirect("/dashboard");
        } else {
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  if (isLoading) {
    return <AppLoading message="Checking authentication..." />;
  }

  return <>{children}</>;
}
