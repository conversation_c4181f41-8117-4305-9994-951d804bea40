import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@repo/ui/components/badge";
import { Icons } from "@repo/ui/components/icons";
import type { ProjectFileResponse } from "@/types";

const getStatusColor = (status?: string) => {
  switch (status) {
    case "Marking":
      return "bg-purple-50 text-purple-700";
    case "Marked":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return "-";
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
};

export const markTableConfig = (): ColumnDef<ProjectFileResponse>[] => [
  // File Name column
  {
    accessorKey: "name",
    header: "File Name",
    cell: ({ row }) => (
      <div className="max-w-[200px]">
        <div className="truncate font-medium text-gray-900" title={row.original?.name}>
          {row.original?.name}
        </div>
      </div>
    ),
    size: 200,
    minSize: 150,
    maxSize: 250,
    meta: {
      isGrow: true,
    },
  },
  // Assets column
  {
    id: "assets",
    header: "Assets",
    cell: ({ row }) => {
      // TODO: Replace with actual assets data from API when available
      return <p className="max-w-[80px] truncate text-gray-600">Machine</p>;
    },
    size: 80,
    meta: {
      isGrow: true,
    },
  },
  // Category column
  {
    id: "category",
    header: "Category",
    cell: ({ row }) => {
      // TODO: Replace with actual category data from API when available
      return <p className="max-w-[80px] truncate text-gray-600">PMS</p>;
    },
    size: 80,
    meta: {
      isGrow: true,
    },
  },
  // File Path column
  {
    accessorKey: "path",
    header: "File Path",
    cell: ({ row }) => (
      <p className="max-w-[200px] truncate text-sm text-gray-600" title={row.original?.path || ""}>
        {row.original?.path || "-"}
      </p>
    ),
    size: 200,
    meta: {
      isGrow: true,
    },
  },
  // Batch column
  {
    id: "batch",
    header: "Batch",
    cell: ({ row }) => {
      // For now, we'll use a placeholder batch number
      // This should be replaced with actual batch data from the API
      return <span className="text-gray-600">1</span>;
    },
    size: 80,
  },
  // Status column
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <div className="flex">
        <Badge variant="secondary" className={`${getStatusColor(row.original?.status)} border-0`}>
          {row.original?.status}
        </Badge>
      </div>
    ),
    size: 120,
  },
  // Actions column
  {
    id: "actions",
    header: "",
    cell: ({ row }) => (
      <div className="flex justify-center">
        <Icons.moreVertical className="h-4 w-4 text-gray-400" />
      </div>
    ),
    size: 40,
  },
];
