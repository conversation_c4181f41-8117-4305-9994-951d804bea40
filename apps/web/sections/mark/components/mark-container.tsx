"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { useRouter } from "next/navigation";
import { MarkTable } from "./mark-table";
import { MarkForm } from "./mark-form";
import { BreadCrumbs } from "@/components/breadcrumbs";
import { BucketNavigation } from "./bucket-navigation";
import type { ProjectFileResponse } from "@/types";
import { Separator } from "@repo/ui/components/separator";
import { useAddToExtract } from "@/sections/file/hooks/api/file.add-to-extract.slice";

interface MarkContainerProps {
  projectId: string;
}

export const MarkContainer = ({ projectId }: MarkContainerProps) => {
  const router = useRouter();
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [selectedFile, setSelectedFile] = useState<ProjectFileResponse | null>(null);
  const addToExtractMutation = useAddToExtract();

  const handleFileSelect = (file: ProjectFileResponse, selected: boolean) => {
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(file.id);
      } else {
        newSet.delete(file.id);
      }
      return newSet;
    });
  };

  const handleRowClick = (file: ProjectFileResponse) => {
    setSelectedFile(file);
  };

  const handleExtract = async () => {
    // If no files are selected, navigate to extract page
    if (selectedFiles.size === 0) {
      router.push(`/project/${projectId}/extract`);
      return;
    }

    // If files are selected, add them to extract
    try {
      await addToExtractMutation.mutateAsync({
        projectId,
        fileIds: Array.from(selectedFiles),
      });

      // Clear selection after successful operation
      setSelectedFiles(new Set());

      // Show success message (you can add toast notification here)
      console.log("Files successfully added to extract");
    } catch (error) {
      console.error("Failed to add files to extract:", error);
      // Show error message (you can add toast notification here)
    }
  };

  const handleFormClose = () => {
    setSelectedFile(null);
  };

  const hasSelectedFiles = selectedFiles.size > 0;

  return (
    <div className="flex h-full flex-row gap-4">
      {/* Main Mark Table */}
      <div className="bg-background flex flex-1 flex-col space-y-4 overflow-hidden rounded-xl px-6 pt-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <BreadCrumbs path={["Mark"]} />
        </div>

        <div className="flex w-full">
          {/* Bucket Navigation */}
          <BucketNavigation projectId={projectId} currentBucket="mark" />
        </div>

        {/* Table Container */}
        <div className="flex min-h-[500px] w-full gap-6 overflow-hidden">
          <div className="flex h-full flex-1 flex-col justify-between">
            <div className="flex-1 overflow-auto">
              <MarkTable
                projectId={projectId}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onRowClick={handleRowClick}
                activeFileId={selectedFile?.id}
              />
            </div>

            {/* Action Button */}
            <div className="flex flex-shrink-0 justify-end py-6">
              <Button
                onClick={handleExtract}
                disabled={addToExtractMutation.isPending}
                className="w-100"
                variant="default"
              >
                {addToExtractMutation.isPending
                  ? "Adding..."
                  : hasSelectedFiles
                    ? `Add to Extract (${selectedFiles.size})`
                    : "Go To Extract"}
              </Button>
            </div>
          </div>
          {/* Mark Form - Right Sidebar */}
          <Separator orientation="vertical" className="h-full" />
          {
            <div className="w-100">
              {selectedFile ? (
                <MarkForm
                  file={selectedFile!}
                  onClose={handleFormClose}
                  onSave={(data) => {
                    console.log("Saving mark data:", data);
                    // TODO: Implement save functionality
                  }}
                />
              ) : (
                <div className="flex h-full flex-col items-center justify-center">
                  <p className="text-muted-foreground text-lg">No file selected</p>
                  <p className="text-muted-foreground text-sm">
                    Select a file to edit marked details
                  </p>
                </div>
              )}
            </div>
          }
        </div>
      </div>
    </div>
  );
};
