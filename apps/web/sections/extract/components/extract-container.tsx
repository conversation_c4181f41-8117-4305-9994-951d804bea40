"use client";
import React, { useState } from "react";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { useRouter } from "next/navigation";
import { ExtractTable } from "./extract-table";
import { ExtractForm } from "./extract-form";
import { BreadCrumbs } from "@/components/breadcrumbs";
import { BucketNavigation } from "./bucket-navigation";
import type { ProjectFileResponse } from "@/types";
import { Separator } from "@repo/ui/components/separator";

interface ExtractContainerProps {
  projectId: string;
}

export const ExtractContainer = ({ projectId }: ExtractContainerProps) => {
  const router = useRouter();
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [selectedFile, setSelectedFile] = useState<ProjectFileResponse | null>(null);

  const handleFileSelect = (file: ProjectFileResponse, selected: boolean) => {
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(file.id);
      } else {
        newSet.delete(file.id);
      }
      return newSet;
    });
  };

  const handleRowClick = (file: ProjectFileResponse) => {
    setSelectedFile(file);
  };

  const handleComplete = () => {
    if (selectedFiles.size === 0) return;
    // Complete extraction process
    console.log("Completing extraction for files:", selectedFiles);
    // TODO: Implement completion functionality
  };

  const handleFormClose = () => {
    setSelectedFile(null);
  };

  const hasSelectedFiles = selectedFiles.size > 0;

  return (
    <div className="flex h-full flex-row gap-4">
      {/* Main Extract Table */}
      <div className="bg-background flex flex-1 flex-col space-y-4 overflow-hidden rounded-xl px-6 pt-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <BreadCrumbs path={["Extract"]} />
        </div>

        <div className="flex w-full">
          {/* Bucket Navigation */}
          <BucketNavigation projectId={projectId} currentBucket="extract" />
        </div>

        {/* Table Container */}
        <div className="flex min-h-[500px] w-full gap-6 overflow-hidden">
          <div className="flex h-full flex-1 flex-col justify-between">
            <div className="flex-1 overflow-auto">
              <ExtractTable
                projectId={projectId}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onRowClick={handleRowClick}
                activeFileId={selectedFile?.id}
              />
            </div>

            {/* Action Button */}
            <div className="flex flex-shrink-0 justify-end py-6">
              <Button
                onClick={handleComplete}
                disabled={!hasSelectedFiles}
                className="w-100"
                variant="default"
              >
                {hasSelectedFiles ? `Complete Extraction (${selectedFiles.size})` : "Complete All"}
              </Button>
            </div>
          </div>
          {/* Extract Form - Right Sidebar */}
          <Separator orientation="vertical" className="h-full" />
          {
            <div className="w-100">
              {selectedFile ? (
                <ExtractForm
                  file={selectedFile!}
                  onClose={handleFormClose}
                  onSave={(data) => {
                    console.log("Saving extract data:", data);
                    // TODO: Implement save functionality
                  }}
                />
              ) : (
                <div className="flex h-full flex-col items-center justify-center">
                  <p className="text-muted-foreground text-lg">No files selected</p>
                  <p className="text-muted-foreground text-sm">
                    Please select files to configure extraction
                  </p>
                </div>
              )}
            </div>
          }
        </div>
      </div>
    </div>
  );
};
