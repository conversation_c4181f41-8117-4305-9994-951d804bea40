"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Icons } from "@repo/ui/components/icons";
import { Progress } from "@repo/ui/components/progress";
import { Separator } from "@repo/ui/components/separator";
import { Skeleton } from "@repo/ui/components/skeleton";
import React from "react";

import type { ProjectResponse } from "@/types";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";

interface ProjectDetailsViewerProps {
  project?: ProjectResponse;
  onClose?: () => void;
}

const ProjectDetailsViewer: React.FC<ProjectDetailsViewerProps> = ({ project, onClose }) => {
  const router = useRouter();
  const formatDate = (date: Date | string) => {
    return dayjs(date).format("DD-MM-YYYY");
  };

  if (!project) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Icons.folder className="text-muted-foreground mx-auto h-12 w-12" />
          <h3 className="mt-4 text-lg font-semibold">No Project Selected</h3>
          <p className="text-muted-foreground">
            Select a project from the table to view its details
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="h-[50px] w-full rounded-xl bg-[#C7ECEB]"></div>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <p className="text-muted-foreground text-sm">
            {project.categories.find((category) => category.glossaryCategoryName === "Client")
              ?.items[0]?.glossaryItemName || "-"}{" "}
            | {project.code}
          </p>
          <h2 className="mt-1 text-xl font-semibold capitalize">{project.vessel}</h2>
        </div>
        {/* {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <Icons.x className="h-4 w-4" />
          </Button>
        )} */}
      </div>

      <Separator />

      {/* Project Info Card */}

      <h1 className="text-base font-semibold text-gray-800">Project Info</h1>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Start Date:</p>
        <p className="text-sm text-gray-600">{formatDate(project.createdAt)}</p>
      </div>
      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Created By:</p>
        <p className="text-sm text-gray-600">{project.assignee?.name || "Not assigned"}</p>
      </div>

      <div className="flex flex-row space-x-2">
        <p className="text-sm font-medium text-gray-600">Last Modified:</p>
        <p className="text-sm text-gray-600">{formatDate(project.updatedAt)}</p>
      </div>

      <Separator />

      <h1 className="text-base font-semibold text-gray-800">Status & Progress</h1>

      <div className="flex flex-row items-center space-x-2">
        <p className="text-sm font-medium text-gray-600">Status:</p>

        <Badge className="mt-1">{project.status}</Badge>
      </div>
      {/* Progress bar placeholder - you can implement actual progress calculation */}
      <Progress value={80} className="w-full" />
      <Separator />
      {/* File Breakdown Card - Placeholder */}

      <h1 className="text-base font-semibold text-gray-800">File Breakdown</h1>

      <div>
        <p className="text-sm font-medium">Total Files:</p>
        <p className="text-muted-foreground text-sm">123</p>
      </div>
      {/* <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-blue-600">Classified:</span>
          <span>43</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-orange-600">Marked:</span>
          <span>50</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-green-600">Extracted:</span>
          <span>70</span>
        </div>
      </div> */}

      {/* Action Button */}
      <Button className="w-full" size="lg" onClick={() => router.push(`/project/${project.id}`)}>
        View Project Detail
      </Button>
    </div>
  );
};

// Skeleton component for loading state
const ProjectDetailsViewerSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      {/* Header Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-6 w-48" />
      </div>

      {/* Project Info Card Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-24" />
        </CardHeader>
        <CardContent className="space-y-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-1">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-32" />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Status Card Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-32" />
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-1">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-2 w-full" />
            <Skeleton className="h-3 w-24" />
          </div>
        </CardContent>
      </Card>

      {/* Categories Card Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-20" />
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-6 w-16" />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* File Breakdown Card Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-28" />
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-1">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-8" />
          </div>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex justify-between">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-6" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Button Skeleton */}
      <Skeleton className="h-10 w-full" />
    </div>
  );
};

export default ProjectDetailsViewer;
export { ProjectDetailsViewerSkeleton };
