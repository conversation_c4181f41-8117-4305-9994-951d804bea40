"use client";

import React, { useState, useCallback, useEffect } from "react";
import ProjectSearchHeader from "./components/project-search-header";
import ProjectList from "./components/project-list";
import ProjectDetailsViewer from "./components/project-details-viewer";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import AddProjectDialog from "./components/dialogs/add-project-dialog";
import EditProjectDialog from "./components/dialogs/edit-project-dialog";
import { CanAccess } from "@/providers/access-control";
import { Action, Resource } from "@/types";
import ProjectSortPopover from "./components/filter-sort/project-sort-popover";
import { ProjectFilterPopover } from "./components/filter-sort/project-filter-popover";
import ActiveFilters from "./components/filter-sort/active-filters";
import ActiveSort from "./components/filter-sort/active-sort";

import type { ProjectResponse } from "@/types";
import { useRouter, useSearchParams } from "next/navigation";
import { useProjectFilterStore } from "@/stores/project-filter-store";

const ProjectPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const hasAddProject = searchParams.has("add-project");

  // Store state
  const selectedProject = useProjectFilterStore((state) => state.selectedProject);
  const setSelectedProject = useProjectFilterStore((state) => state.setSelectedProject);
  const clearSelectedProject = useProjectFilterStore((state) => state.clearSelectedProject);

  // Local state
  const [openAdd, setOpenAdd] = useState(hasAddProject);
  const [editProject, setEditProject] = useState<ProjectResponse>();
  const [openEditDialog, setOpenEditDialog] = useState(false);

  // Clear add-project param from URL
  useEffect(() => {
    if (!hasAddProject) return;
    setOpenAdd(true);
    const params = new URLSearchParams(searchParams);
    params.delete("add-project");
    router.replace(`${window.location.pathname}?${params}`, { scroll: false });
  }, [hasAddProject, router, searchParams]);

  const handleRowClick = useCallback(
    (project: ProjectResponse) => {
      setSelectedProject(project);
    },
    [setSelectedProject]
  );

  const handleCloseDetails = useCallback(() => {
    setSelectedProject(undefined);
  }, [setSelectedProject]);

  const handleEditProject = useCallback((project: ProjectResponse) => {
    setEditProject(project);
    setOpenEditDialog(true);
  }, []);

  return (
    <div className="flex h-full flex-col space-y-2">
      <ProjectSearchHeader />

      <SubNavBar>
        <ActiveFilters />
        <ActiveSort />

        <ProjectSortPopover />
        <ProjectFilterPopover />

        <CanAccess privilege={"create" as Action.Create} resource={"project" as Resource.Project}>
          <Button onClick={() => setOpenAdd(true)} className="bg-primary text-white">
            Add Project
            <Icons.plus />
          </Button>
        </CanAccess>
      </SubNavBar>

      <div className="flex flex-row gap-4">
        <div className="flex-1">
          <ProjectList
            onRowClick={handleRowClick}
            selectedProject={selectedProject}
            onEditProject={handleEditProject}
          />
        </div>
        <div className="bg-background min-h-[500px] w-[385px] space-y-4 rounded-xl p-6">
          <ProjectDetailsViewer project={selectedProject} onClose={handleCloseDetails} />
        </div>
      </div>

      <AddProjectDialog open={openAdd} setOpen={setOpenAdd} />
      <EditProjectDialog
        open={openEditDialog}
        setOpen={setOpenEditDialog}
        selectedProject={editProject}
      />
    </div>
  );
};

export default ProjectPage;
