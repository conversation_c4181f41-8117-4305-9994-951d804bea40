"use client";

import React, { useState } from "react";
import { getFileColumns } from "../config/table-config";
import { ErrorBoundary } from "@/components/error-boundary";
import { But<PERSON> } from "@repo/ui/components/button";
import FileTableSkeleton from "./skeleton/file-table-skeleton";
import AdvancedTable from "@/components/advanced-table";
import type { ProjectFileResponse, ProjectGetResponse } from "@/types";
import { ProjectFilesDetailsViewer } from "./project-files-detail-viewer";
import NoFileData from "./no-data/no-file-data";
import { Icons } from "@repo/ui/components/icons";
import { useFileFilterStore } from "@/stores/file-filter-store";
import { useProjectData } from "@/sections/project/hooks";
import { useFilteredFiles, useAddToClassify } from "../hooks/api";
import { useRouter } from "next/navigation";

interface FileTableProps {
  projectId: string;
}

const FileTable: React.FC<FileTableProps> = ({ projectId }) => {
  const router = useRouter();

  // Get file filters from store
  const filters = useFileFilterStore((state) => state.filters);
  const sort = useFileFilterStore((state) => state.sort);

  // Fetch project details at top level
  const {
    data: projectData,
    isLoading: isProjectLoading,
    error: projectError,
    refetch: refetchProject,
  } = useProjectData(projectId);

  // Fetch file list at top level
  const {
    files,
    isLoading: isFilesLoading,
    error: filesError,
    hasNextPage,
    isFetchingNextPage,
    refetch: refetchFiles,
    handleScrollEnd,
  } = useFilteredFiles({
    projectId,
    limit: 20,
    filters,
    sort,
  });

  // Local state for selected files
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  // Handle file selection
  const handleFileSelect = (file: ProjectFileResponse, selected: boolean) => {
    setSelectedFiles((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(file.id);
      } else {
        newSet.delete(file.id);
      }
      return newSet;
    });
  };

  // Initialize mutation hook
  const addToClassifyMutation = useAddToClassify();

  // Handle classify action - either add selected files to classify or navigate to classify page
  const handleClassify = async () => {
    // If no files are selected, navigate to classify page
    if (selectedFiles.size === 0) {
      router.push(`/project/${projectId}/classify`);
      return;
    }

    // If files are selected, add them to classify
    try {
      await addToClassifyMutation.mutateAsync({
        projectId,
        fileIds: Array.from(selectedFiles),
      });

      // Clear selection after successful operation
      setSelectedFiles(new Set());

      // Show success message (you can add toast notification here)
      console.log("Files successfully added to classify");
    } catch (error) {
      console.error("Failed to add files to classify:", error);
      // Show error message (you can add toast notification here)
    }
  };

  const columns = getFileColumns();

  if (isProjectLoading || isFilesLoading) {
    return <FileTableSkeleton />;
  }

  const handleRetry = () => {
    refetchFiles();
    refetchProject();
  };

  if (projectError || filesError) {
    return <ErrorBoundary error={projectError || filesError} onRetry={() => handleRetry()} />;
  }

  return (
    <div className="flex h-full flex-row gap-4">
      {/* Main Table */}
      <div className="bg-background flex flex-1 flex-col space-y-4 overflow-hidden rounded-xl px-6 pt-6">
        {/* Header - commented out but keep for reference */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Source Documents</h2>
            <p className="text-sm text-gray-500">
              Total Records - {projectData?.fileCount || files.length}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="rounded-2xl text-gray-500">
              Export
              <Icons.download className="h-4 w-4" />
            </Button>
            {/* <FileSortPopover />
            <FileFilterPopover /> */}
          </div>
        </div>
        {/* Table Container */}
        <div className="flex-1 overflow-hidden">
          {files.length === 0 ? (
            <NoFileData />
          ) : (
            <div className="flex h-full flex-col justify-between">
              <div className="flex-1 overflow-auto">
                <AdvancedTable<ProjectFileResponse>
                  data={files}
                  columns={columns}
                  onLoadMore={handleScrollEnd}
                  hasNextPage={hasNextPage}
                  isFetchingNextPage={isFetchingNextPage}
                  onRowSelect={handleFileSelect}
                  selectedRows={selectedFiles}
                  getRowId={(file) => file.id}
                  isRowDisabled={(file) =>
                    file.status !== "Uploaded" && file.status !== "Cancelled"
                  }
                  className="mt-4"
                />
              </div>
              <div className="flex flex-shrink-0 justify-end py-6">
                <Button
                  onClick={handleClassify}
                  disabled={addToClassifyMutation.isPending}
                  className="w-100"
                  variant="default"
                >
                  {addToClassifyMutation.isPending ? (
                    <>
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      {selectedFiles.size > 0 ? "Adding to Classify..." : "Loading..."}
                    </>
                  ) : selectedFiles.size > 0 ? (
                    `Add to Classify (${selectedFiles.size})`
                  ) : (
                    "Go To Classify"
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sidebar */}
      <ProjectFilesDetailsViewer projectData={projectData} />
    </div>
  );
};

export default FileTable;
