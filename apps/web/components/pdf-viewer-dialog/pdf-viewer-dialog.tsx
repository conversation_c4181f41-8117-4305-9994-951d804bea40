"use client";

import { useEffect, useState } from "react";
import { Di<PERSON>, DialogContent } from "@repo/ui/components/dialog";
import { PdfViewer, type PageMark, type BBox } from "@/components/pdf-viewer/pdf-viewer";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Icons } from "@repo/ui/components/icons";
import { instance } from "@/axios-instance";

interface PdfViewerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  fileId: string;
  fileName?: string;
}

export function PdfViewerDialog({
  open,
  onOpenChange,
  projectId,
  fileId,
  fileName,
}: PdfViewerDialogProps) {
  const [marks, setMarks] = useState<PageMark[]>([]);
  const [bboxes, setBboxes] = useState<Record<number, BBox[]>>({});
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Create authenticated PDF URL using blob
  useEffect(() => {
    if (!open || !projectId || !fileId) {
      return;
    }

    const createAuthenticatedUrl = async () => {
      setIsLoading(true);
      setPdfError(null);
      
      try {
        const response = await instance.get(`/projects/${projectId}/files/${fileId}`, {
          responseType: "blob",
        });
        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
      } catch (err) {
        console.error("Failed to load PDF:", err);
        setPdfError("Failed to load PDF file");
      } finally {
        setIsLoading(false);
      }
    };

    createAuthenticatedUrl();

    // Cleanup function
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    };
  }, [open, projectId, fileId]);

  // Cleanup on dialog close
  useEffect(() => {
    if (!open && pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
      setMarks([]);
      setBboxes({});
      setPdfError(null);
    }
  }, [open, pdfUrl]);

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="max-w-[95vw] max-h-[95vh] h-[95vh] w-[95vw] p-0"
        showCloseButton={false}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b bg-white px-6 py-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {fileName || "PDF Viewer"}
              </h2>
              <p className="text-sm text-gray-500">
                Select job and spare parts for this document
              </p>
            </div>
            <button
              onClick={handleClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
            >
              <Icons.x className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            {isLoading && (
              <div className="flex h-full w-full items-center justify-center">
                <div className="space-y-4">
                  <Skeleton className="h-8 w-48" />
                  <Skeleton className="h-96 w-80" />
                </div>
              </div>
            )}

            {pdfError && (
              <div className="flex h-full w-full items-center justify-center">
                <div className="flex max-w-md items-center gap-3 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800">
                  <Icons.alertCircle className="h-5 w-5 flex-shrink-0" />
                  <p className="text-sm">{pdfError}</p>
                </div>
              </div>
            )}

            {pdfUrl && !isLoading && !pdfError && (
              <div className="h-full">
                <PdfViewer
                  pdfUrl={pdfUrl}
                  value={marks}
                  onChange={setMarks}
                  bboxes={bboxes}
                  onBBoxesChange={setBboxes}
                  className="h-full"
                />
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
