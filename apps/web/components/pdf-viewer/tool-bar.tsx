"use client";

import { Minus, Plus, RotateCw } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import type { ToolbarProps } from "./types";

export function Toolbar({
  pageNum,
  scale,
  onZoomIn,
  onZoomOut,
  onRotate,
  checked,
  onChangeChecked,
  disabled,
}: ToolbarProps) {
  return (
    <div
      className={cn(
        "bg-secondary text-foreground ring-border flex items-center justify-between rounded-md px-3 py-2 ring-1",
        disabled && "opacity-60"
      )}
    >
      <div className="flex items-center gap-2">
        <button
          type="button"
          aria-label="Zoom out"
          onClick={onZoomOut}
          disabled={disabled}
          className="bg-card ring-border hover:bg-accent inline-flex h-7 w-7 items-center justify-center rounded ring-1"
        >
          <Minus className="h-4 w-4" />
        </button>
        <span className="min-w-[56px] text-center text-sm tabular-nums">
          {Math.round(scale * 100)}%
        </span>
        <button
          type="button"
          aria-label="Zoom in"
          onClick={onZoomIn}
          disabled={disabled}
          className="bg-card ring-border hover:bg-accent inline-flex h-7 w-7 items-center justify-center rounded ring-1"
        >
          <Plus className="h-4 w-4" />
        </button>
      </div>

      <div className="flex items-center gap-4">
        <label className="flex items-center gap-2 text-sm">
          <input
            type="checkbox"
            className="h-4 w-4 accent-[var(--color-foreground)]"
            checked={checked.job}
            onChange={(e) => onChangeChecked("job", e.target.checked)}
            disabled={disabled}
            aria-label={`Job checkbox${pageNum ? ` for page ${pageNum}` : ""}`}
          />
          <span>Job</span>
        </label>
        <label className="flex items-center gap-2 text-sm">
          <input
            type="checkbox"
            className="h-4 w-4 accent-[var(--color-foreground)]"
            checked={checked.spare}
            onChange={(e) => onChangeChecked("spare", e.target.checked)}
            disabled={disabled}
            aria-label={`Spare checkbox${pageNum ? ` for page ${pageNum}` : ""}`}
          />
          <span>Spare</span>
        </label>
      </div>

      <div className="flex items-center gap-2">
        <button
          type="button"
          aria-label="Rotate"
          onClick={onRotate}
          disabled={disabled}
          className="bg-card ring-border hover:bg-accent inline-flex h-7 w-7 items-center justify-center rounded ring-1"
        >
          <RotateCw className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
