"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { ChevronLeft, ChevronRight, Trash2 } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { getDocument, type PDFDocumentProxy, type RenderTask } from "pdfjs-dist";
import { usePdfWorker } from "./usePdfWorker";
import { clamp } from "./utils";
import { Button } from "@repo/ui/components/button";

/* ----------------------------- Types ----------------------------- */

export interface PageMark {
  page: number;
  job: boolean;
  spare: boolean;
  rotation?: 0 | 90 | 180 | 270;
}

export interface BBox {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: 0 | 90 | 180 | 270;
}

export interface PdfViewerProps {
  pdfUrl: string;
  initialPage?: number;
  className?: string;
  value: PageMark[];
  onChange: (marks: PageMark[]) => void;
  bboxes: Record<number, BBox[]>;
  onBBoxesChange: (bboxes: Record<number, BBox[]>) => void;
}

/* ----------------------------- Component ----------------------------- */

export function PdfViewer({
  pdfUrl,
  initialPage = 1,
  className,
  value,
  onChange,
  bboxes,
  onBBoxesChange,
}: PdfViewerProps) {
  usePdfWorker();

  const [doc, setDoc] = useState<PDFDocumentProxy | null>(null);
  const [numPages, setNumPages] = useState(0);
  const [leftPage, setLeftPage] = useState(() =>
    Math.max(1, initialPage - (initialPage % 2 === 0 ? 1 : 0))
  );
  const [scale, setScale] = useState(1);
  const [rotationMap, setRotationMap] = useState<Record<number, 0 | 90 | 180 | 270>>({});
  const [loadingError, setLoadingError] = useState<string | null>(null);

  const [drawing, setDrawing] = useState<{
    startX: number;
    startY: number;
    x: number;
    y: number;
    width: number;
    height: number;
    page: number | null;
  } | null>(null);

  const leftCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const rightCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const leftOverlayRef = useRef<HTMLDivElement | null>(null);
  const rightOverlayRef = useRef<HTMLDivElement | null>(null);
  const leftTaskRef = useRef<RenderTask | null>(null);
  const rightTaskRef = useRef<RenderTask | null>(null);

  /* ----------------------------- Navigation ----------------------------- */
  const nextSpread = () => setLeftPage((p) => (p + 2 <= numPages ? p + 2 : p));
  const prevSpread = () => setLeftPage((p) => (p - 2 >= 1 ? p - 2 : p));

  const zoomIn = () => setScale((s) => clamp(Number((s + 0.1).toFixed(2)), 0.5, 3));
  const zoomOut = () => setScale((s) => clamp(Number((s - 0.1).toFixed(2)), 0.5, 3));

  /* ----------------------------- Rotation ----------------------------- */
  const rotatePage = (pageNum: number) => {
    setRotationMap((prev) => {
      const current = prev[pageNum] ?? 0;
      const newRotation = ((current + 90) % 360) as 0 | 90 | 180 | 270;

      // ✅ Update PageMark
      const updatedMarks = value.map((m) =>
        m.page === pageNum ? { ...m, rotation: newRotation } : m
      );
      const exists = value.find((m) => m.page === pageNum);
      if (!exists)
        updatedMarks.push({
          page: pageNum,
          job: false,
          spare: false,
          rotation: newRotation,
        });
      onChange(updatedMarks);

      // ✅ Update BBoxes (store rotation for each box)
      const updatedBBoxes = {
        ...bboxes,
        [pageNum]: (bboxes[pageNum] || []).map((b) => ({
          ...b,
          rotation: newRotation,
        })),
      };
      onBBoxesChange(updatedBBoxes);

      return { ...prev, [pageNum]: newRotation };
    });
  };

  /* ----------------------------- Page Marks ----------------------------- */
  const getMarkForPage = (page: number) =>
    value.find((v) => v.page === page) || { page, job: false, spare: false, rotation: 0 };

  const setCheck = (pageNum: number, key: "job" | "spare", val: boolean) => {
    const updated = value.map((m) => (m.page === pageNum ? { ...m, [key]: val } : m));
    const exists = value.find((m) => m.page === pageNum);
    if (!exists)
      updated.push({
        page: pageNum,
        job: key === "job" ? val : false,
        spare: key === "spare" ? val : false,
        rotation: rotationMap[pageNum] ?? 0,
      });
    onChange(updated);
  };

  /* ----------------------------- PDF Load ----------------------------- */
  useEffect(() => {
    let cancelled = false;
    setLoadingError(null);

    getDocument({ url: pdfUrl })
      .promise.then((pdf) => {
        if (cancelled) return;
        setDoc(pdf);
        setNumPages(pdf.numPages);
        setLeftPage((prev) => (prev % 2 === 0 ? prev - 1 : prev));
      })
      .catch(() => setLoadingError("Failed to load PDF."));

    return () => {
      cancelled = true;
    };
  }, [pdfUrl]);

  const rightPage = useMemo(
    () => (leftPage + 1 <= numPages ? leftPage + 1 : null),
    [leftPage, numPages]
  );

  /* ----------------------------- Rendering ----------------------------- */
  const cancelRenderTasks = useCallback(() => {
    leftTaskRef.current?.cancel();
    rightTaskRef.current?.cancel();
    leftTaskRef.current = null;
    rightTaskRef.current = null;
  }, []);

  const renderPageToCanvas = useCallback(
    async (pageNum: number, canvas: HTMLCanvasElement | null): Promise<RenderTask | null> => {
      if (!doc || !canvas) return null;
      try {
        const page = await doc.getPage(pageNum);
        const dpr = window.devicePixelRatio || 1;
        const rotation = rotationMap[pageNum] ?? 0;
        const viewport = page.getViewport({ scale, rotation });
        const ctx = canvas.getContext("2d", { alpha: false });
        if (!ctx) return null;

        canvas.width = viewport.width * dpr;
        canvas.height = viewport.height * dpr;
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;

        ctx.scale(dpr, dpr);
        const task = page.render({ canvasContext: ctx, viewport });
        await task.promise;
        return task;
      } catch {
        return null;
      }
    },
    [doc, scale, rotationMap]
  );

  useEffect(() => {
    if (!doc) return;
    cancelRenderTasks();
    (async () => {
      leftTaskRef.current = await renderPageToCanvas(leftPage, leftCanvasRef.current);
      if (rightPage) {
        rightTaskRef.current = await renderPageToCanvas(rightPage, rightCanvasRef.current);
      }
    })();
    return cancelRenderTasks;
  }, [doc, leftPage, rightPage, scale, rotationMap, renderPageToCanvas]);

  /* ----------------------------- Drawing ----------------------------- */
  const handleMouseDown = (e: React.MouseEvent, page: number) => {
    const overlay = e.currentTarget as HTMLDivElement;
    const rect = overlay.getBoundingClientRect();
    const startX = e.clientX - rect.left;
    const startY = e.clientY - rect.top;
    setDrawing({ startX, startY, x: startX, y: startY, width: 0, height: 0, page });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!drawing) return;
    const overlay = drawing.page === leftPage ? leftOverlayRef.current : rightOverlayRef.current;
    if (!overlay) return;
    const rect = overlay.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    setDrawing({
      ...drawing,
      x: Math.min(currentX, drawing.startX),
      y: Math.min(currentY, drawing.startY),
      width: Math.abs(currentX - drawing.startX),
      height: Math.abs(currentY - drawing.startY),
    });
  };

  const handleMouseUp = () => {
    if (!drawing || drawing.width < 5 || drawing.height < 5) {
      setDrawing(null);
      return;
    }
    const page = drawing.page!;
    const rotation = rotationMap[page] ?? 0;
    onBBoxesChange({
      ...bboxes,
      [page]: [...(bboxes[page] || []), { ...drawing, rotation }],
    });
    setDrawing(null);
  };

  /* ----------------------------- Render ----------------------------- */
  return (
    <div className={cn("relative w-full max-w-[1400px] rounded-xl shadow-xl", className)}>
      <div className="flex gap-8 p-6">
        {/* Left Page */}
        <div className="relative flex-1">
          <Toolbar
            pageNum={leftPage}
            scale={scale}
            onZoomIn={zoomIn}
            onZoomOut={zoomOut}
            onRotate={() => rotatePage(leftPage)}
            checked={getMarkForPage(leftPage)}
            onChangeChecked={(key, val) => setCheck(leftPage, key, val)}
            onClearBBoxes={() =>
              onBBoxesChange({
                ...bboxes,
                [leftPage]: [],
              })
            }
          />
          <PageCanvas
            pageNum={leftPage}
            canvasRef={leftCanvasRef}
            overlayRef={leftOverlayRef}
            bboxes={bboxes[leftPage] || []}
            drawing={drawing}
            handleMouseDown={handleMouseDown}
            handleMouseMove={handleMouseMove}
            handleMouseUp={handleMouseUp}
          />
        </div>

        {/* Right Page */}
        <div className="relative flex-1">
          <Toolbar
            pageNum={rightPage ?? undefined}
            scale={scale}
            onZoomIn={zoomIn}
            onZoomOut={zoomOut}
            onRotate={() => rightPage && rotatePage(rightPage)}
            disabled={!rightPage}
            checked={rightPage ? getMarkForPage(rightPage) : { page: 0, job: false, spare: false }}
            onChangeChecked={(key, val) => rightPage && setCheck(rightPage, key, val)}
            onClearBBoxes={() => {
              if (!rightPage) return;
              onBBoxesChange({
                ...bboxes,
                [rightPage]: [],
              });
            }}
          />
          {rightPage ? (
            <PageCanvas
              pageNum={rightPage}
              canvasRef={rightCanvasRef}
              overlayRef={rightOverlayRef}
              bboxes={bboxes[rightPage] || []}
              drawing={drawing}
              handleMouseDown={handleMouseDown}
              handleMouseMove={handleMouseMove}
              handleMouseUp={handleMouseUp}
            />
          ) : (
            <div className="text-muted-foreground h-40 text-center">No next page</div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <button
        aria-label="Previous pages"
        onClick={prevSpread}
        className="bg-secondary text-foreground hover:bg-muted absolute left-2 top-1/2 -translate-y-1/2 rounded-full p-2 shadow"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      <button
        aria-label="Next pages"
        onClick={nextSpread}
        className="bg-secondary text-foreground hover:bg-muted absolute right-2 top-1/2 -translate-y-1/2 rounded-full p-2 shadow"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Global Clear All */}
      <div className="flex justify-center pb-4">
        <Button variant="destructive" onClick={() => onBBoxesChange({})}>
          <Trash2 className="mr-2 h-4 w-4" /> Clear All BBoxes
        </Button>
      </div>

      {loadingError && (
        <div className="border-border text-destructive-foreground border-t p-3 text-sm">
          {loadingError}
        </div>
      )}
    </div>
  );
}

/* ----------------------------- Toolbar ----------------------------- */
function Toolbar({
  pageNum,
  scale,
  onZoomIn,
  onZoomOut,
  onRotate,
  checked,
  onChangeChecked,
  onClearBBoxes,
  disabled,
}: {
  pageNum?: number;
  scale: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onRotate: () => void;
  checked: { job: boolean; spare: boolean };
  onChangeChecked: (key: "job" | "spare", val: boolean) => void;
  onClearBBoxes?: () => void;
  disabled?: boolean;
}) {
  return (
    <div className="flex items-center justify-between gap-2">
      <div className="flex items-center gap-2">
        <Button onClick={onZoomOut} variant="outline" size="icon">
          -
        </Button>
        <Button onClick={onZoomIn} variant="outline" size="icon">
          +
        </Button>
        <Button onClick={onRotate} variant="outline" size="icon">
          ⟳
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.job}
            onChange={(e) => onChangeChecked("job", e.target.checked)}
          />
          Job
        </label>
        <label className="flex items-center gap-1 text-sm">
          <input
            type="checkbox"
            checked={checked.spare}
            onChange={(e) => onChangeChecked("spare", e.target.checked)}
          />
          Spare
        </label>
      </div>

      <Button
        onClick={onClearBBoxes}
        variant="destructive"
        size="sm"
        disabled={disabled}
        className="flex items-center gap-1"
      >
        <Trash2 className="h-4 w-4" /> Clear
      </Button>
    </div>
  );
}

/* ----------------------------- Page Canvas ----------------------------- */
function PageCanvas({
  pageNum,
  canvasRef,
  overlayRef,
  bboxes,
  drawing,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
}: {
  pageNum: number;
  canvasRef: React.RefObject<HTMLCanvasElement | null>;
  overlayRef: React.RefObject<HTMLDivElement | null>;
  bboxes: BBox[];
  drawing: any;
  handleMouseDown: (e: React.MouseEvent, page: number) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  handleMouseUp: () => void;
}) {
  return (
    <div className="bg-background ring-border relative mt-3 flex items-center justify-center rounded-lg p-3 ring-1">
      <canvas ref={canvasRef} />
      <div
        ref={overlayRef}
        className="absolute inset-0 cursor-crosshair"
        onMouseDown={(e) => handleMouseDown(e, pageNum)}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        {bboxes.map((b, i) => (
          <div
            key={i}
            className="absolute border-2 border-blue-500 bg-blue-500/10"
            style={{
              left: b.x,
              top: b.y,
              width: b.width,
              height: b.height,
            }}
          />
        ))}
        {drawing?.page === pageNum && (
          <div
            className="absolute border-2 border-green-500 bg-green-500/10"
            style={{
              left: drawing.x,
              top: drawing.y,
              width: drawing.width,
              height: drawing.height,
            }}
          />
        )}
      </div>
    </div>
  );
}
