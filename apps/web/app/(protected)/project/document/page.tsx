"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { SubNavBar } from "@/components/sub-nav-bar";
import { redirect } from "next/navigation";
import DocumentFiles from "@/sections/project/document-table";

export default function DocumentPage() {
  const [open, setOpen] = useState(false);
  return (
    <div className="h-fit w-full bg-[#f0f7f6]">
      <SearchBar />

      <Dialog open={open} onOpenChange={setOpen}>
        <div className="py-3">
          <SubNavBar
            actionButton={[{ name: `+  Upload Files`, onClick: () => redirect("/login") }]}
          />
        </div>

        <DocumentFiles />

        <DialogContent className="sm:max-w-[800px]">{/* <AddPeojectFormView /> */}</DialogContent>
      </Dialog>
    </div>
  );
}
